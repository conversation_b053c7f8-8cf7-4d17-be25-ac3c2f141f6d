import java.util.Properties

// __BY_ME__
import com.android.build.gradle.api.ApplicationVariant
import com.android.build.gradle.internal.api.BaseVariantOutputImpl

plugins {
    alias(libs.plugins.android.application)
    alias(libs.plugins.kotlin.android)
    alias(libs.plugins.navigation.safeargs)
    id("kotlin-parcelize")
}

android {
    namespace = "local.bestoon"
    compileSdk = 35

    defaultConfig {
        applicationId = "local.bestoon"
        minSdk = 33
        targetSdk = 35
        versionCode = 2
        versionName = "2.0.0"

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
    }

    // __BY_ME__ custom apk name
    applicationVariants.all {
        val variant = this
        outputs.all {
            val output = this as BaseVariantOutputImpl
            val applicationId = variant.applicationId
            val versionName = variant.versionName ?: "0.0"
            val versionCode = variant.versionCode
            val buildType = variant.buildType.name
            val flavorName = variant.flavorName?.takeIf { it.isNotBlank() } ?: "noflavor"

            val fileName = "${applicationId}-${flavorName}-${buildType}-v${versionName}-${versionCode}.apk"
            output.outputFileName = fileName
        }
    }

    // __BY_ME__
    // conditionally set url
    flavorDimensions += "default"
    productFlavors {
        create("emulator") {
            dimension = "default"

            manifestPlaceholders["BACKUP_IS_ALLOWED"] = "true"
            manifestPlaceholders["USES_CLEAR_TEXT_TRAFFIC"] = "true"

            buildConfigField("String", "BASE_URL", "\"http://10.0.2.2:8000\"")
        }
        create("phone") {
            dimension = "default"

            manifestPlaceholders["BACKUP_IS_ALLOWED"] = "true"
            manifestPlaceholders["USES_CLEAR_TEXT_TRAFFIC"] = "true"

            buildConfigField("String", "BASE_URL", "\"http://192.168.1.100:8000\"")
        }
        create("production") {
            dimension = "default"

            // STEPs for BASE_URL:

            // STEP 1/4: load .env and validate
            val envFile = rootProject.file(".env")
            val envProps = Properties().apply {
                if (envFile.exists()) {
                    envFile.inputStream().use { load(it) }
                }
            }

            // STEP 2/4: get PRODUCTION_BASE_URL
            val productionBaseUrl = envProps.getProperty("PRODUCTION_BASE_URL")

            // STEP 3/4: validate PRODUCTION_BASE_URL
            if (productionBaseUrl.isNullOrBlank()) {
                throw GradleException(
                    "Missing or empty PRODUCTION_BASE_URL in .env file.\n" +
                    "Please create/update the .env file in the project root with a line like:\n" +
                    "PRODUCTION_BASE_URL=https://your.example.ir"
                )
            }

            // STEP 3.5/4:
            // for security purposes, we must set
            // USES_CLEAR_TEXT_TRAFFIC to false.
            // but there are times when even on production
            // productionBaseUrl starts with http://, either
            //   - for testing purposes, or
            //   - because user is on a local network
            //     and wants to connect to a local service like <NAME>.service.
            // so let's set USES_CLEAR_TEXT_TRAFFIC accrodingly
            // otherwise authentication would be impossible on such networks.
            // logic: so, if productionBaseUrl starts with https://,
            //        USES_CLEAR_TEXT_TRAFFIC is automatically set to false,
            //        otherwise true
            val isHttps = productionBaseUrl.startsWith("https://")
            manifestPlaceholders["USES_CLEAR_TEXT_TRAFFIC"] = (!isHttps).toString()
            manifestPlaceholders["BACKUP_IS_ALLOWED"]       = "false"

            // STEP 4/4: set BASE_URL
            buildConfigField("String", "BASE_URL", "\"$productionBaseUrl\"")
        }
    }

    buildTypes {
        getByName("release") {  // __BY_ME__ release -> getByName("release")

            // __BY_ME__
            isDebuggable = false

            isMinifyEnabled = true  // __BY_ME__ false -> true
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )

            // __BY_ME__
            buildConfigField("Boolean", "DEBUG", "false")
        }

        // __BY_ME__
        getByName("debug") {
            buildConfigField("Boolean", "DEBUG", "true")
        }
    }
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }
    kotlinOptions {
        jvmTarget = "11"
    }
    buildFeatures {
        viewBinding = true
        buildConfig = true
    }
}

dependencies {

    implementation(libs.androidx.core.ktx)
    implementation(libs.androidx.appcompat)
    implementation(libs.material)
    implementation(libs.androidx.constraintlayout)
    implementation(libs.androidx.lifecycle.livedata.ktx)
    implementation(libs.androidx.lifecycle.viewmodel.ktx)
    implementation(libs.androidx.navigation.fragment.ktx)
    implementation(libs.androidx.navigation.ui.ktx)

    // Network
    implementation(libs.retrofit)
    implementation(libs.retrofit.gson)
    implementation(libs.okhttp)
    implementation(libs.okhttp.logging)
    implementation(libs.gson)

    // Coroutines
    implementation(libs.kotlinx.coroutines.core)
    implementation(libs.kotlinx.coroutines.android)

    // Biometric
    implementation(libs.androidx.biometric)

    // MPAndroidChart
    implementation("com.github.PhilJay:MPAndroidChart:v3.1.0")

    testImplementation(libs.junit)
    androidTestImplementation(libs.androidx.junit)
    androidTestImplementation(libs.androidx.espresso.core)
}
