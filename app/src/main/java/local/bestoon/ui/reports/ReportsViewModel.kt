package local.bestoon.ui.reports

import android.util.Log
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.launch
import local.bestoon.data.SessionManager
import local.bestoon.data.api.ApiService
import local.bestoon.data.model.ReportsResponse
import local.bestoon.data.model.ChartDataResponse

/**
 * ViewModel for the Reports screen.
 */
class ReportsViewModel(
    private val apiService: ApiService,
    private val sessionManager: SessionManager
) : ViewModel() {

    private val _reportsData = MutableLiveData<ReportsResponse>()
    val reportsData: LiveData<ReportsResponse> = _reportsData

    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading

    private val _errorMessage = MutableLiveData<String>()
    val errorMessage: LiveData<String> = _errorMessage

    // Chart data LiveData
    private val _monthlyChartData = MutableLiveData<ChartDataResponse>()
    val monthlyChartData: LiveData<ChartDataResponse> = _monthlyChartData

    private val _bankChartData = MutableLiveData<ChartDataResponse>()
    val bankChartData: LiveData<ChartDataResponse> = _bankChartData

    private val _categoryChartData = MutableLiveData<ChartDataResponse>()
    val categoryChartData: LiveData<ChartDataResponse> = _categoryChartData

    private val _tagChartData = MutableLiveData<ChartDataResponse>()
    val tagChartData: LiveData<ChartDataResponse> = _tagChartData

    /**
     * Loads reports data from the API with optional filter parameters.
     */
    fun loadReportsData() {
        val token = sessionManager.getAuthToken()
        if (token == null) {
            Log.e("ReportsViewModel", "No auth token available")
            _errorMessage.value = "Authentication token not available"
            return
        }

        viewModelScope.launch {
            _isLoading.value = true
            _errorMessage.value = ""

            try {
                // Get saved filter parameters
                val chosenYear = sessionManager.getChosenYear()
                val chosenMonthStart = sessionManager.getChosenMonthStart()
                val chosenMonthEnd = sessionManager.getChosenMonthEnd()

                val response = apiService.getReports(
                    token = "Token $token",
                    year = chosenYear,
                    monthStart = chosenMonthStart,
                    monthEnd = chosenMonthEnd
                )

                if (response.isSuccessful) {
                    val data = response.body()
                    if (data != null) {
                        _reportsData.value = data
                        // Save the returned filter parameters
                        sessionManager.saveChosenYear(data.chosenyear)
                        sessionManager.saveChosenMonthStart(data.chosenmonthstart)
                        sessionManager.saveChosenMonthEnd(data.chosenmonthend)
                    } else {
                        Log.e("ReportsViewModel", "Response body is null")
                        _errorMessage.value = "No data received from server"
                    }
                } else {
                    Log.e("ReportsViewModel", "API call failed: ${response.code()} - ${response.message()}")
                    _errorMessage.value = "Failed to load data: ${response.message()}"
                }
            } catch (e: Exception) {
                Log.e("ReportsViewModel", "Error loading reports data", e)
                _errorMessage.value = "Network error: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Updates filter parameters and reloads data.
     */
    fun updateFilters(year: Int?, monthStart: Int?, monthEnd: Int?) {
        // Save the new filter parameters
        year?.let { sessionManager.saveChosenYear(it) }
        monthStart?.let { sessionManager.saveChosenMonthStart(it) }
        monthEnd?.let { sessionManager.saveChosenMonthEnd(it) }

        // Reload data with new filters
        loadReportsData()
        loadAllChartData()
    }

    /**
     * Clears the month end filter and reloads data.
     */
    fun clearMonthEndFilter() {
        // Clear the month end filter parameter
        sessionManager.clearChosenMonthEnd()

        // Reload data with cleared month end filter
        loadReportsData()
        loadAllChartData()
    }

    /**
     * Refreshes reports data.
     */
    fun refreshReportsData() {
        loadReportsData()
        loadAllChartData()
    }

    /**
     * Loads all chart data.
     */
    fun loadAllChartData() {
        loadChartData("monthly-sum") { _monthlyChartData.value = it }
        loadChartData("bank-sum") { _bankChartData.value = it }
        loadChartData("category-sum") { _categoryChartData.value = it }
        loadChartData("tag-sum") { _tagChartData.value = it }
    }

    /**
     * Loads chart data for a specific chart type.
     */
    private fun loadChartData(chartType: String, onSuccess: (ChartDataResponse) -> Unit) {
        val token = sessionManager.getAuthToken()
        if (token == null) {
            Log.e("ReportsViewModel", "No auth token available for chart data")
            return
        }

        viewModelScope.launch {
            try {
                // Get saved filter parameters
                val chosenYear = sessionManager.getChosenYear()
                val chosenMonthStart = sessionManager.getChosenMonthStart()
                val chosenMonthEnd = sessionManager.getChosenMonthEnd()

                val response = apiService.getChartData(
                    token = "Token $token",
                    year = chosenYear,
                    monthStart = chosenMonthStart,
                    monthEnd = chosenMonthEnd,
                    chartType = chartType
                )

                if (response.isSuccessful) {
                    val data = response.body()
                    if (data != null) {
                        onSuccess(data)
                    } else {
                        Log.e("ReportsViewModel", "Chart data response body is null for $chartType")
                    }
                } else {
                    Log.e("ReportsViewModel", "Chart data API call failed for $chartType: ${response.code()} - ${response.message()}")
                }
            } catch (e: Exception) {
                Log.e("ReportsViewModel", "Error loading chart data for $chartType", e)
            }
        }
    }
}
